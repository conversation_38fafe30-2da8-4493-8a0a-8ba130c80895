package com.redberyl.invoiceapp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.redberyl.invoiceapp.util.IdConverter;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
public class InvoiceDto extends BaseDto {
    private Long id;

    @NotBlank(message = "Invoice number is required")
    private String invoiceNumber;

    @NotNull(message = "Client ID is required")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Object clientId;
    private ClientDto client;

    public Long getClientId() {
        return IdConverter.extractId(clientId);
    }

    public void setClientId(Object clientId) {
        this.clientId = clientId;
    }

    @NotNull(message = "Invoice type ID is required")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Object invoiceTypeId;
    private InvoiceTypeDto invoiceType;

    public Long getInvoiceTypeId() {
        return IdConverter.extractId(invoiceTypeId);
    }

    public void setInvoiceTypeId(Object invoiceTypeId) {
        this.invoiceTypeId = invoiceTypeId;
    }

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Object projectId;
    private ProjectDto project;

    public Long getProjectId() {
        return projectId != null ? IdConverter.extractId(projectId) : null;
    }

    public void setProjectId(Object projectId) {
        this.projectId = projectId;
    }

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Object candidateId;
    private CandidateDto candidate;

    public Long getCandidateId() {
        return candidateId != null ? IdConverter.extractId(candidateId) : null;
    }

    public void setCandidateId(Object candidateId) {
        this.candidateId = candidateId;
    }

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Object staffingTypeId;
    private StaffingTypeDto staffingType;

    public Long getStaffingTypeId() {
        return staffingTypeId != null ? IdConverter.extractId(staffingTypeId) : null;
    }

    public void setStaffingTypeId(Object staffingTypeId) {
        this.staffingTypeId = staffingTypeId;
    }

    @NotNull(message = "Billing amount is required")
    @Positive(message = "Billing amount must be positive")
    private Object billingAmount;

    @NotNull(message = "Tax amount is required")
    @Positive(message = "Tax amount must be positive")
    private Object taxAmount;

    @NotNull(message = "Total amount is required")
    @Positive(message = "Total amount must be positive")
    private Object totalAmount;

    @NotNull(message = "Invoice date is required")
    private Object invoiceDate;

    public BigDecimal getBillingAmount() {
        if (billingAmount instanceof BigDecimal) {
            return (BigDecimal) billingAmount;
        } else if (billingAmount instanceof Number) {
            return BigDecimal.valueOf(((Number) billingAmount).doubleValue());
        } else if (billingAmount instanceof String) {
            try {
                return new BigDecimal((String) billingAmount);
            } catch (NumberFormatException e) {
                return BigDecimal.ZERO;
            }
        }
        return BigDecimal.ZERO;
    }

    public BigDecimal getTaxAmount() {
        if (taxAmount instanceof BigDecimal) {
            return (BigDecimal) taxAmount;
        } else if (taxAmount instanceof Number) {
            return BigDecimal.valueOf(((Number) taxAmount).doubleValue());
        } else if (taxAmount instanceof String) {
            try {
                return new BigDecimal((String) taxAmount);
            } catch (NumberFormatException e) {
                return BigDecimal.ZERO;
            }
        }
        return BigDecimal.ZERO;
    }

    public BigDecimal getTotalAmount() {
        if (totalAmount instanceof BigDecimal) {
            return (BigDecimal) totalAmount;
        } else if (totalAmount instanceof Number) {
            return BigDecimal.valueOf(((Number) totalAmount).doubleValue());
        } else if (totalAmount instanceof String) {
            try {
                return new BigDecimal((String) totalAmount);
            } catch (NumberFormatException e) {
                return BigDecimal.ZERO;
            }
        }
        return BigDecimal.ZERO;
    }

    public LocalDate getInvoiceDate() {
        if (invoiceDate instanceof LocalDate) {
            return (LocalDate) invoiceDate;
        } else if (invoiceDate instanceof String) {
            try {
                return LocalDate.parse((String) invoiceDate);
            } catch (Exception e) {
                return LocalDate.now();
            }
        }
        return LocalDate.now();
    }

    private Object dueDate;
    private Boolean isRecurring;
    private Boolean publishedToFinance;
    private Object publishedAt;

    public LocalDate getDueDate() {
        if (dueDate instanceof LocalDate) {
            return (LocalDate) dueDate;
        } else if (dueDate instanceof String) {
            try {
                return LocalDate.parse((String) dueDate);
            } catch (Exception e) {
                return LocalDate.now().plusDays(30);
            }
        }
        return LocalDate.now().plusDays(30);
    }

    public LocalDateTime getPublishedAt() {
        if (publishedAt instanceof LocalDateTime) {
            return (LocalDateTime) publishedAt;
        } else if (publishedAt instanceof String) {
            try {
                return LocalDateTime.parse((String) publishedAt);
            } catch (Exception e) {
                return null;
            }
        }
        return null;
    }

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Object hsnId;
    private HsnCodeDto hsnCode;

    public Long getHsnId() {
        return hsnId != null ? IdConverter.extractId(hsnId) : null;
    }

    public void setHsnId(Object hsnId) {
        this.hsnId = hsnId;
    }

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Object redberylAccountId;
    private RedberylAccountDto redberylAccount;

    public Long getRedberylAccountId() {
        return redberylAccountId != null ? IdConverter.extractId(redberylAccountId) : null;
    }

    public void setRedberylAccountId(Object redberylAccountId) {
        this.redberylAccountId = redberylAccountId;
    }

    private Integer attendanceDays;
}
